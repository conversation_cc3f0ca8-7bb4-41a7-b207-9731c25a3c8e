# YisiSUP - 依思数字权益商城

> **免责申明**
>
> 本仓库仅供学习交流使用，任何人或组织不得将本仓库内容用于非法用途或侵犯他人合法权益，否则后果自负。对于本因使用本仓库而引起的任何法律责任，作者均不承担任何责任。使用本仓库的内容即表示您已阅读并理解本免责申明的所有条款和条件。
> 本仓库中的插件功能相关技术文档面向广大开发者与公众，开发者在进行开发时，插件内容不得违法我国法律法规或侵犯他人合法权益，因插件所造成的任何后果(含相关法律责任)均由开发者自行承担。公众使用本仓库内容时，应仔细查看插件内相对应的申明条款，遵守相关规定。

## 项目简介
一个基础**数字权益商城项目**，支持对接市场主流的权益商品货源(持续更新)与自营商品
### 技术原理
- 后端采用**Django Web框架**进行开发
- 前端采用**HTML/Vue.js**混合渲染(因模板差异而定)，Vue模板使用**Vite**进行构建

## 🚀快速开始

### 环境要求

- Python 3.8+
- Django 5.2+
- NodeJs 18+
- MySQL 8.0+

### 宝塔快速部署

#### 1.克隆项目源码

```bash
# 在需要保存的目录终端运行
git clone https://github.com/A-YiS/python-django-yisisup.git
# 或者使用Gitee(码云)拉取(国内推荐)
git clone https://gitee.com/labixiaoqiu/python-django-yisisup.git
```

#### 2.创建环境与项目

##### 安装NodeJs环境

###### 宝塔面板应用市场安装

1. 右侧导航栏进入**软件商店** 搜索**Node.js版本管理器**并安装
2. 打开**Node.js版本管理器**设置 选择v18+版本安装
3. 安装完成后上方命令行版本选择对应的版本

NodeJs 18+ 版本需要
因操作系统版本原因(如CentOS 7)无法安装NodeJs 18+可查看 [低版本系统及NodeJs更改依赖版本的运行解决方案](#NodeJs_version_solution)

##### 终端命令安装

```bash
# 更新系统
sudo apt update
sudo apt upgrade -y

# 下载并安装 nvm
curl -o- https://raw.githubusercontent.com/nvm-sh/nvm/v0.39.1/install.sh | bash

# 加载 nvm
source ~/.nvm/nvm.sh

# 使用 nvm 安装 Node.js
nvm install node # 安装最新版本的 Node.js
nvm use node     # 使用最新版本的 Node.js

# 或者安装特定版本
nvm install 14 # 安装 Node.js 版本 14
nvm use 14     # 使用 Node.js 版本 14

# 切换到不同的版本
nvm use <version>
```

##### 创建宝塔Web项目
导航栏网站 -> Python项目 -> Python版本管理 -> 选择对应及以上版本安装<br>
添加Python项目 -> 选择对应路径与Python环境版本<br>

```bash
# 启动方式：命令行启动
# 启动命令：
python run.py runserver # 默认占用8000端口启动
# 或者 接收指定端口启动
python run.py runserver 8080 # 占用8080端口运行django
# 注意不能占用9000端口启动django命令 9000端口为固定插件交互端口
# 环境变量可直接选择 无

# 安装依赖包如若宝塔没有自动填充，则手动选择项目文件根目录下的requirements.txt文件
# 更多设置 -> 项目初始化命令：
python deploy.py production # 生成deploy_config.yaml Django环境配置文件

# (其实下面可以不用 在新版中已经集成到了python deploy.py production命令中)
# 安装node.js依赖
cd frontend
npm install
# 构建前端资源
npm run build # 静态文件输出至/static/vue/目录下
```

##### 安装Python依赖包
```bash
# 安装Python依赖 -> 注意虚拟环境影响
# 如果添加项目时没有自动检测到依赖需要手动运行
# 一般情况下 创建项目时会自动检测
pip install -r requirements.txt
```

#### 3.deploy_config.yaml文件配置

导航栏 -> 数据库 -> MySQL-> 添加数据库

**注意MySQL版本要求8.0+ 配合 Django 5.2+**
> 宝塔的MySql环境貌似默认安装的是**5.7**版本

返回项目目录 点击deploy_config.yaml进入编辑，按照实际情况填写所有配置信息

```bash
# Django环境配置 -> 注意虚拟环境影响
# 如果没有找到deploy_config.yaml文件可执行
python deploy.py production
# 如果宝塔在创建项目时执行了该初始化指令应该已经存在deploy_config.yaml文件

# 编辑完成deploy_config.yaml文件后保存 再次执行
python deploy.py production
```
**python deploy.py production** 命令执行内容可查看 [deploy.py命令说明](#deploy_py_command)

<a id="deploy_py_command"></a>

##### deploy.py命令配置生产环境(production)解析

1. 创建deploy_config.yaml文件 (如果文件不存在)
> 文件内容可跳转 [deploy_config.yaml文件内容](#context_deploy_config_yaml) 查看
> 如果自动创建文件失败可以复制内容手动创建

2. 生成生产环境配置文件 (.env)
调用内置yaml解析器解析deploy_config.yaml文件内容
- 生成安全的 **SECRET_KEY**（50位随机字符）
- 生成 **JWT_SECRET_KEY**（64位随机字符）
- 生成 **ALLOWED_HOSTS**（包含localhost, 127.0.0.1, 服务器IP, 域名）

3. 静态文件收集
- 执行 Django 的 **python manage.py collectstatic --noinput** 命令
 > 将所有静态文件收集到  **staticfiles** 目录

4. NodeJs依赖安装与前端资源构建
- 检查NodeJs环境 **node -v** 与 **npm -v**
- 安装前端依赖 **npm install**
- 构建前端资源 **npm run build**
- 验证构建输出 **static/vue/** 目录存在

#### 4. 启动服务

再次回到宝塔网站页面，点击启动对应的项目就可以啦！

### NodeJs版本及依赖降级
推荐**NodeJs 18+**版本运行<br>
最低支持**Ubuntu 18.04 LTS**或**CentOS 7**以上版本

**请根据自己的NodeJs版本查看对应方法**
> 按照方法替换项目文件夹内frontend\package.json文件内容

- [NodeJs 16+降级配置更改](#change_nodejs_16)
- [NodeJs 14+降级配置更改](#change_nodejs_14)

** 替换package.json文件内如下内容**
```json
  "dependencies": {
    "@vue/runtime-dom": "^3.4.0",
    "js-md5": "^0.7.3",
    "vue": "^3.4.0"
  },
  "devDependencies": {
    "@babel/core": "^7.23.0",
    "@babel/preset-env": "^7.23.0",
    "@vitejs/plugin-vue": "^5.0.0",
    "autoprefixer": "^10.4.0",
    "concurrently": "^8.2.0",
    "postcss": "^8.4.0",
    "sass": "^1.69.0",
    "terser": "^5.43.1",
    "vite": "^5.0.0"
  },
 ```

#### NodeJs 16+降级配置更改<a id="change_nodejs_16"></a>

```
  "devDependencies": {
    "@babel/core": "^7.23.0",
    "@babel/preset-env": "^7.23.0",
    "@vitejs/plugin-vue": "^4.6.2",
    "autoprefixer": "^10.4.0",
    "concurrently": "^8.2.0",
    "postcss": "^8.4.0",
    "sass": "^1.69.0",
    "terser": "^5.43.1",
    "vite": "^4.5.3"
  },
  "dependencies": {
    "@vue/runtime-dom": "^3.4.0",
    "js-md5": "^0.7.3",
    "vue": "^3.4.0"
  }
```
- @vitejs/plugin-vue  5.x → 4.x
- vite  5.x → 4.x

#### NodeJs 14+降级配置更改<a id="change_nodejs_14"></a>

```json
  "devDependencies": {
    "@babel/core": "^7.23.0",
    "@babel/preset-env": "^7.23.0",
    "@vitejs/plugin-vue": "^4.6.2",
    "autoprefixer": "^10.4.0",
    "concurrently": "^7.6.0",
    "postcss": "^8.4.0",
    "sass": "^1.63.6",
    "terser": "^5.43.1",
    "vite": "^4.5.3"
  },
  "dependencies": {
    "@vue/runtime-dom": "^3.3.13",
    "js-md5": "^0.7.3",
    "vue": "^3.3.13"
  }
```

- @vitejs/plugin-vue  5.x → 4.x
- concurrently  8.x → 7.x
- sass  1.69.x → 1.63.x
- vite  5.x → 4.x
- @vue/runtime-dom  3.4.x → 3.3.x
- vue  3.4.x → 3.3.x

### 其他说明

#### deploy_config.yaml文件内容<a id="context_deploy_config_yaml"></a>
```yaml
# Django 项目部署配置文件
# 请根据实际情况填写以下配置信息

# 数据库配置
database:
  engine: django.db.backends.mysql
  name: ''          # 数据库名称
  user: ''          # 数据库用户名
  password: ''      # 数据库密码
  host: localhost
  port: '3306'

# 邮件配置
email:
  host: ''          # SMTP服务器，如: smtp.qq.com
  port: 465
  use_ssl: true
  user: ''          # 发送邮件的邮箱账号
  password: ''      # 邮箱授权码

# 管理员配置
admin:
  username: ''      # 后台管理员用户名
  password: ''      # 后台管理员密码

# 服务器配置
server:
  ip: ''            # 服务器IP地址
  domain: ''        # 域名（可选）
  port: 8000        # 服务端口
```
**可根据实际情况手动复制内容并创建文件**

### 特别鸣谢
- **依思.** [个人QQ](https://qm.qq.com/q/pwCx2npAOI '点击跳转QQ个人页')   [GitHub主页](https://github.com/A-YiS '点击跳转GitHub主页')   [Gitee主页](https://gitee.com/A-YiS '点击跳转Gitee主页')